.ai-highlights_1134-1960 {
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
  border-radius: 8px;
  border-color: #f1f5f7;
  border-style: solid;
  border-width: 1px;
  background: #ffffff;
  width: -webkit-fill-available;
}
.frame-913_I1134-1960-924-2187 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-end;
  gap: 0px;
  box-sizing: border-box;
  width: -webkit-fill-available;
}
.head_I1134-1960-924-2188 {
  padding: 0px 0px 12px 0px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 20px;
  box-sizing: border-box;
  width: -webkit-fill-available;
}
.stack_I1134-1960-924-2189 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 10px;
  box-sizing: border-box;
}
.text-label_I1134-1960-924-2190 {
  color: #17181a;
  font-weight: 600;
  text-align: left;
  text-wrap: nowrap;
}

.table_I1134-1960-924-2191 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
  width: -webkit-fill-available;
}
.columns_I1134-1960-924-2192 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
  width: -webkit-fill-available;
}
.column_I1134-1960-924-2193 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
}
.header-item_I1134-1960-924-2194 {
  padding: 0px 8px 0px 8px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 10px;
  box-sizing: border-box;
  border-color: #f1f5f7;
  border-style: solid;
  border-bottom-width: 1px;
  height: 40px;
  width: -webkit-fill-available;
}
.table-item_I1134-1960-924-2195 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 40px;
}
.text-label_I1134-1960-924-2197 {
  color: #17181a;
  font-weight: 500;
  text-align: left;
  text-wrap: nowrap;
}

.table-item_I1134-1960-924-2198 {
  padding: 10px 8px 10px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff3f;
  height: 40px;
  width: -webkit-fill-available;
}
.icon-text_I1134-1960-924-2199 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
}
.text-label_I1134-1960-924-2200 {
  color: #17181a;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.table-item_I1134-1960-924-2201 {
  padding: 10px 8px 10px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
  height: 40px;
  width: -webkit-fill-available;
}
.icon-text_I1134-1960-924-2202 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
}
.text-label_I1134-1960-924-2203 {
  color: #17181a;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.table-item_I1134-1960-924-2204 {
  padding: 10px 8px 10px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
  height: 40px;
  width: -webkit-fill-available;
}
.icon-text_I1134-1960-924-2205 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
}
.text-label_I1134-1960-924-2206 {
  color: #17181a;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.column_I1134-1960-1202-8084 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
}
.header-item_I1134-1960-1202-8085 {
  padding: 0px 8px 0px 8px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 10px;
  box-sizing: border-box;
  border-color: #f1f5f7;
  border-style: solid;
  border-bottom-width: 1px;
  height: 40px;
  width: -webkit-fill-available;
}
.table-item_I1134-1960-1202-8086 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 40px;
}
.text-label_I1134-1960-1202-8088 {
  color: #17181a;
  font-weight: 500;
  text-align: left;
  text-wrap: nowrap;
}

.table-item_I1134-1960-1202-8089 {
  padding: 10px 8px 10px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff3f;
  height: 40px;
  width: -webkit-fill-available;
}
.icon-text_I1134-1960-1202-8090 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
}
.text-label_I1134-1960-1202-8091 {
  color: #17181a;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.table-item_I1134-1960-1202-8092 {
  padding: 10px 8px 10px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
  height: 40px;
  width: -webkit-fill-available;
}
.icon-text_I1134-1960-1202-8093 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
}
.text-label_I1134-1960-1202-8094 {
  color: #17181a;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.table-item_I1134-1960-1202-8095 {
  padding: 10px 8px 10px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
  height: 40px;
  width: -webkit-fill-available;
}
.icon-text_I1134-1960-1202-8096 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
}
.text-label_I1134-1960-1202-8097 {
  color: #17181a;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.column_I1134-1960-924-2207 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
}
.header-item_I1134-1960-924-2208 {
  padding: 0px 8px 0px 8px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 10px;
  box-sizing: border-box;
  border-color: #f1f5f7;
  border-style: solid;
  border-bottom-width: 1px;
  height: 40px;
}
.table-item_I1134-1960-924-2209 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 40px;
}
.text-label_I1134-1960-924-2211 {
  color: #17181a;
  font-weight: 500;
  text-align: left;
  text-wrap: nowrap;
}

.table-item_I1134-1960-924-2212 {
  padding: 10px 8px 10px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff3f;
  height: 40px;
  width: -webkit-fill-available;
}
.icon-text_I1134-1960-924-2213 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
}
.text-label_I1134-1960-924-2214 {
  color: #17181a;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.table-item_I1134-1960-924-2215 {
  padding: 10px 8px 10px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
  height: 40px;
  width: -webkit-fill-available;
}
.icon-text_I1134-1960-924-2216 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
  width: 22px;
}
.text-label_I1134-1960-924-2217 {
  color: #17181a;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 72px;
}

.table-item_I1134-1960-924-2218 {
  padding: 10px 8px 10px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
  height: 40px;
  width: -webkit-fill-available;
}
.icon-text_I1134-1960-924-2219 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
  width: 22px;
}
.text-label_I1134-1960-924-2220 {
  color: #17181a;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 72px;
}

.column_I1134-1960-924-2221 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
}
.header-item_I1134-1960-924-2222 {
  padding: 0px 8px 0px 8px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 10px;
  box-sizing: border-box;
  border-color: #f1f5f7;
  border-style: solid;
  border-bottom-width: 1px;
  height: 40px;
}
.table-item_I1134-1960-924-2223 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 40px;
}
.text-label_I1134-1960-924-2225 {
  color: #17181a;
  font-weight: 500;
  text-align: left;
  text-wrap: nowrap;
}

.table-item_I1134-1960-924-2226 {
  padding: 10px 8px 10px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff3f;
  height: 40px;
  width: -webkit-fill-available;
}
.icon-text_I1134-1960-924-2227 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
}
.text-label_I1134-1960-924-2228 {
  color: #17181a;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.table-item_I1134-1960-924-2229 {
  padding: 10px 8px 10px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
  height: 40px;
  width: -webkit-fill-available;
}
.icon-text_I1134-1960-924-2230 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
}
.text-label_I1134-1960-924-2231 {
  color: #17181a;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 16px;
}

.table-item_I1134-1960-924-2232 {
  padding: 10px 8px 10px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
  height: 40px;
  width: -webkit-fill-available;
}
.icon-text_I1134-1960-924-2233 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
}
.text-label_I1134-1960-924-2234 {
  color: #17181a;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.column_I1134-1960-924-2235 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
}
.header-item_I1134-1960-924-2236 {
  padding: 0px 8px 0px 8px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 10px;
  box-sizing: border-box;
  border-color: #f1f5f7;
  border-style: solid;
  border-bottom-width: 1px;
  height: 40px;
  width: -webkit-fill-available;
}
.table-item_I1134-1960-924-2237 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 40px;
}
.text-label_I1134-1960-924-2239 {
  color: #17181a;
  font-weight: 500;
  text-align: left;
  text-wrap: nowrap;
}

.table-item_I1134-1960-924-2240 {
  padding: 10px 8px 10px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff3f;
  height: 40px;
  width: -webkit-fill-available;
}
.icon-text_I1134-1960-924-2241 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
}
.text-label_I1134-1960-924-2242 {
  color: #0071bc;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.table-item_I1134-1960-924-2243 {
  padding: 10px 8px 10px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
  height: 40px;
  width: -webkit-fill-available;
}
.icon-text_I1134-1960-924-2244 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
}
.text-label_I1134-1960-924-2245 {
  color: #0071bc;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.table-item_I1134-1960-924-2246 {
  padding: 10px 8px 10px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
  height: 40px;
  width: -webkit-fill-available;
}
.icon-text_I1134-1960-924-2247 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
}
.text-label_I1134-1960-924-2248 {
  color: #0071bc;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.column_I1134-1960-924-2266 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
  width: -webkit-fill-available;
}
.column_I1134-1960-924-2267 {
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex-wrap: nowrap;
  align-items: center;
  gap: 0px;
  box-sizing: border-box;
  width: -webkit-fill-available;
}
.header-item_I1134-1960-924-2268 {
  padding: 0px 8px 0px 8px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 10px;
  box-sizing: border-box;
  border-color: #f1f5f7;
  border-style: solid;
  border-bottom-width: 1px;
  width: -webkit-fill-available;
}
.table-item_I1134-1960-924-2269 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 40px;
  width: -webkit-fill-available;
}
.text-label_I1134-1960-924-2270 {
  color: #17181a;
  font-weight: 500;
  text-align: left;
  text-wrap: wrap;
  width: 54px;
}

.table-item_I1134-1960-924-2272 {
  padding: 18px 8px 18px 8px;
  display: flex;
  flex-direction: row;
  justify-content: center;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
  height: 40px;
  width: -webkit-fill-available;
}
.check_I1134-1960-924-2273 {
  position: relative;
  width: 16px;
  height: 16px;
}

.table-item_I1134-1960-924-2274 {
  padding: 0px 8px 0px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 40px;
}
.check_I1134-1960-924-2275 {
  position: relative;
  width: 16px;
  height: 16px;
}

.table-item_I1134-1960-924-2276 {
  padding: 0px 8px 0px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 40px;
}
.check_I1134-1960-924-2277 {
  position: relative;
  width: 16px;
  height: 16px;
}
