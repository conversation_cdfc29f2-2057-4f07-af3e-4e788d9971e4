@use 'variables' as variables;
@use 'mixins' as mix;

.component-test-container {
  max-width: 1600px; // Increased from 1400px to accommodate full demographics width (1380px) + padding
  margin: 0 auto;
  padding: 30px;
  background-color: variables.$background-gray;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  line-height: 32px;
  color: variables.$text-black;
  margin-bottom: 16px;
}

.page-description {
  font-size: 14px;
  font-weight: 300;
  line-height: 20px;
  color: variables.$text-black;
  margin-bottom: 30px;
}

.component-section {
  @include mix.card;
  margin-bottom: 30px;
}

.section-title {
  font-size: 20px;
  font-weight: 600;
  line-height: 32px;
  color: variables.$text-black;
  margin-bottom: 20px;
}

.section-description {
  font-size: 14px;
  color: variables.$gray-3;
  margin-bottom: 24px;
  line-height: 1.5;
}

.subsection-description {
  font-size: 14px;
  color: variables.$gray-3;
  margin-bottom: 16px;
  line-height: 1.5;
}

.subsection-title {
  font-size: 16px;
  font-weight: 600;
  line-height: 24px;
  color: variables.$text-black;
  margin: 24px 0 16px 0;
  padding-left: 12px;
  border-left: 3px solid variables.$primary-blue;
}

.component-subtitle {
  font-size: 11px;
  font-weight: 300;
  line-height: 16px;
  color: variables.$gray-3;
  margin-bottom: 12px;
  font-style: italic;
}

.component-row {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 40px; // Increased spacing to accommodate expanded dropdown/calendar states
  min-width: 100%; // Ensure full width utilization

  &:last-child {
    margin-bottom: 0;
  }

  // Special handling for demographics rows that need full width
  &.demographics-row {
    flex-wrap: nowrap;
    overflow-x: visible;
  }
}

.component-item {
  flex: 1;
  min-width: 300px;

  &.full-width {
    flex: 0 0 100%;
  }
}

.component-title {
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
  color: variables.$text-black;
  margin-bottom: 12px;
}

.component-demo {
  padding: 20px;
  background-color: variables.$white;
  border-radius: variables.$border-radius-lg;
  outline: variables.$border-width-default variables.$gray-1 solid;
  outline-offset: -(variables.$border-width-default);
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 80px;
  overflow-x: visible; // Prevent horizontal scrollbars, allow content to extend naturally

  // Special handling for tables
  &.table-demo {
    justify-content: flex-start;
    align-items: flex-start;
    padding: 0;
    display: block;
  }

  // Figma-specific sizing for components
  &.figma-sized {
    justify-content: flex-start;
    align-items: flex-start;

    // Button containers - match Figma button sizing
    &.button-demo {
      width: fit-content;
      min-width: auto;
      padding: 20px;
    }

    // Form control containers - match Figma form sizing
    &.form-demo {
      width: 300px; // Standard form width from Figma
      min-width: 300px;
      min-height: 400px; // Increased height to accommodate expanded dropdowns and calendars
      padding: 20px 20px 60px 20px; // Extra bottom padding for expanded content
      overflow: visible; // Allow dropdown/calendar popups to extend beyond container

      app-dropdown,
      app-date-picker,
      app-notes {
        width: 100%;
      }

      // Ensure dropdown and date picker have adequate space for expanded states
      app-dropdown,
      app-date-picker {
        position: relative;
        z-index: 10; // Ensure popups appear above other content
      }
    }

    // AI Highlights component - match Figma AI Highlights table sizing
    &.ai-highlights-demo {
      width: 557px; // Figma: 517px + 40px padding
      min-width: 557px;
      padding: 20px;

      app-ai-highlights {
        width: 517px; // Exact Figma width
      }
    }

    // Demographics component - responsive width
    &.demographics-demo {
      width: 100%; // Allow full width to be responsive
      padding: 20px;
      overflow-x: visible; // Prevent horizontal scrollbars
    }

    // Results container - match Figma results sizing
    &.results-demo {
      width: 400px; // Estimated from Figma
      min-width: 400px;
      padding: 20px;
    }

    // Menu component - full width as per Figma
    &.menu-demo {
      width: 100%;
      padding: 0;
    }

    // Status indicators - minimal sizing
    &.status-demo {
      width: fit-content;
      min-width: auto;
      padding: 20px;
    }

    // Checkbox - minimal sizing
    &.checkbox-demo {
      width: fit-content;
      min-width: auto;
      padding: 20px;
    }
  }
}

.component-code {
  background-color: variables.$light-background;
  border-radius: variables.$border-radius-lg;
  padding: 12px;
  overflow: auto;

  pre {
    margin: 0;
    font-family: monospace;
    font-size: 12px;
    line-height: 1.5;
    color: variables.$gray-4;
  }
}

.full-width {
  width: 100%;
}

// Responsive styles
@include mix.for-tablet-portrait-up {
  .component-row {
    flex-wrap: nowrap;
  }
}

@include mix.for-phone-only {
  .component-item {
    flex: 0 0 100%;
  }
}

// Icon demo specific styles
.icons-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 16px;
  padding: 16px;
}

.icon-demo-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 12px;
  border: 1px solid variables.$gray-1;
  border-radius: 8px;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: variables.$gray-1;
  }
}

.icon-label {
  font-size: 10px;
  font-family: 'Urbane', sans-serif;
  font-weight: 300;
  color: variables.$gray-3;
  text-align: center;
}

