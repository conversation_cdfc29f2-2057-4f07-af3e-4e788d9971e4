Free
Code
Log

view

code

style

tokens
.demographics_1202-5816 {
  position: relative;
  box-sizing: border-box;
  overflow: hidden;
  background: #ffffff;
  width: 1831px;
  height: 932px;
}
.text-spacing-_1202-5817 {
  top: 407px;
  left: 45px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 18px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 600;
  text-align: left;
  width: 83px;
  height: 16px;
}

.text-styling-_1202-5818 {
  top: 669px;
  left: 45px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 18px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 600;
  text-align: left;
  width: 83px;
  height: 16px;
}

.text-behavior--scrolls-with-the-page--clicking-the-back-button-goes-back-to-the-dashboard-_1202-5819 {
  top: 170px;
  left: 44px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 18px;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  text-align: left;
  width: 1040px;
  height: 16px;
}

.rectangle-287_1202-5820 {
  top: 218px;
  left: 45px;
  position: absolute;
  display: flex;
  display: flex;
}

.rectangle-289_1202-5821 {
  top: 717px;
  left: 116px;
  position: absolute;
  display: flex;
  display: flex;
}

.rectangle-288_1202-5822 {
  top: 444px;
  left: 45px;
  position: absolute;
  display: flex;
  display: flex;
}

.demographics_1238-2011 {
  top: 236px;
  left: 94px;
  position: absolute;
  display: flex;
  width: 1380px;
}

.text-usage--demographics-header-that-is-used-on-all-chart-review-pages--the-demographics-information-populate-based-on-the-member-chart-_1202-5823 {
  top: 130px;
  left: 45px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 18px;
  letter-spacing: 0%;
  text-decoration: none;
  text-align: left;
  text-wrap: wrap;
  width: 1478px;
  height: 22px;
}

.text-demographics-v3_1202-5824 {
  top: 63px;
  left: 45px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 36px;
  font-family: Urbane;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 600;
  text-align: left;
  text-wrap: nowrap;
  width: 339px;
  height: 43px;
}

.vector_1202-5825 {
  top: 574px;
  left: 192px;
  position: absolute;
  display: flex;
  display: flex;
}

.vector_1202-5826 {
  top: 574px;
  left: 334px;
  position: absolute;
  display: flex;
  display: flex;
}

.vector_1202-5827 {
  top: 574px;
  left: 444px;
  position: absolute;
  display: flex;
  display: flex;
}

.vector_1202-5828 {
  top: 574px;
  left: 553px;
  position: absolute;
  display: flex;
  display: flex;
}

.vector_1202-5829 {
  top: 574px;
  left: 632px;
  position: absolute;
  display: flex;
  display: flex;
}

.vector_1202-5830 {
  top: 574px;
  left: 729px;
  position: absolute;
  display: flex;
  display: flex;
}

.vector_1202-5831 {
  top: 574px;
  left: 876px;
  position: absolute;
  display: flex;
  display: flex;
}

.demographics_1238-2672 {
  top: 736px;
  left: 187px;
  position: absolute;
  display: flex;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 20px;
  box-sizing: border-box;
  width: 1380px;
}
.table_1238-2673 {
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
  border-radius: 8px;
  border-color: #f1f5f7;
  border-style: solid;
  border-width: 1px;
  background: #ffffff;
  width: -webkit-fill-available;
}
.frame-925_1238-2892 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 48px;
  box-sizing: border-box;
  width: -webkit-fill-available;
}
.frame-927_1238-2893 {
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 48px;
  box-sizing: border-box;
}
.frame-928_1238-2894 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 36px;
  box-sizing: border-box;
}
.back-to-home_1238-2895 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 8px;
  box-sizing: border-box;
}
.icon-arrow_1238-2896 {
  position: relative;
  box-sizing: border-box;
  overflow: hidden;
  display: flex;
}
.vector_1238-2897 {
  top: 5px;
  left: 4px;
  position: absolute;
  display: flex;
  display: flex;
}

.text-text_1238-2898 {
  color: #0071bc;
  font-weight: 500;
  text-align: left;
  text-wrap: nowrap;
}

.member-id_1238-2899 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: -4px;
  box-sizing: border-box;
}
.text-55820474_1238-2900 {
  color: #17181a;
  font-size: 20px;
  font-family: Urbane;
  line-height: 160.0000023841858%;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 600;
  text-align: left;
  text-wrap: wrap;
  width: -webkit-fill-available;
}

.text-member-id_1238-2901 {
  color: #547996;
  font-weight: 500;
  text-align: left;
  text-wrap: wrap;
  width: -webkit-fill-available;
}

.frame-922_1238-2902 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 55px;
  box-sizing: border-box;
}
.frame-924_1238-2903 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 40px;
  box-sizing: border-box;
}
.frame-916_1238-2904 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: -4px;
  box-sizing: border-box;
}
.text-john-dey_1238-2905 {
  color: #17181a;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 600;
  text-align: left;
  text-wrap: wrap;
  width: -webkit-fill-available;
}

.text-member_1238-2906 {
  color: #547996;
  font-weight: 500;
  text-align: left;
  text-wrap: wrap;
  width: -webkit-fill-available;
}

.frame-917_1238-2907 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: -4px;
  box-sizing: border-box;
  width: 69px;
}
.text-01-05-1972_1238-2908 {
  color: #17181a;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 600;
  text-align: left;
  text-wrap: wrap;
  width: -webkit-fill-available;
}

.text-dob_1238-2909 {
  color: #547996;
  font-weight: 500;
  text-align: left;
  text-wrap: wrap;
  width: -webkit-fill-available;
}

.frame-918_1238-2910 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: -4px;
  box-sizing: border-box;
}
.text-m_1238-2911 {
  color: #17181a;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 600;
  text-align: left;
  text-wrap: wrap;
  width: -webkit-fill-available;
}

.text-gender_1238-2912 {
  color: #547996;
  font-weight: 500;
  text-align: left;
  text-wrap: nowrap;
}

.frame-919_1238-2913 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: -4px;
  box-sizing: border-box;
  width: 51px;
}
.text-mahmo_1238-2914 {
  color: #17181a;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 600;
  text-align: left;
  text-wrap: wrap;
  width: -webkit-fill-available;
}

.text-lob_1238-2915 {
  color: #547996;
  font-weight: 500;
  text-align: left;
  text-wrap: wrap;
  width: -webkit-fill-available;
}

.frame-923_1238-2916 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 24px;
  box-sizing: border-box;
}
.frame-921_1238-2917 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: -4px;
  box-sizing: border-box;
}
.text-nicolas-dejong_1238-2918 {
  color: #17181a;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 600;
  text-align: left;
  text-wrap: wrap;
  width: -webkit-fill-available;
}

.text-provider_1238-2919 {
  color: #547996;
  font-weight: 500;
  text-align: left;
  text-wrap: wrap;
  width: -webkit-fill-available;
}

.frame-920_1238-2920 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: -4px;
  box-sizing: border-box;
  width: 68px;
}
.text-882716229_1238-2921 {
  color: #17181a;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 600;
  text-align: left;
  text-wrap: wrap;
  width: -webkit-fill-available;
}

.text-npi_1238-2922 {
  color: #547996;
  font-weight: 500;
  text-align: left;
  text-wrap: wrap;
  width: -webkit-fill-available;
}

.vector_1202-5832 {
  top: 462px;
  left: 83px;
  position: absolute;
  display: flex;
  display: flex;
}

.vector_1202-5833 {
  top: 529px;
  left: 1520px;
  position: absolute;
  display: flex;
  display: flex;
}

.text-88px_1202-5834 {
  top: 499px;
  left: 45px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
  width: 32px;
  height: 4px;
}

.text-items-aligned-top-left_1202-5835 {
  top: 505px;
  left: 1525px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
  width: 129px;
  height: 4px;
}

.text-36px_1202-5836 {
  top: 582px;
  left: 194px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
  width: 31px;
  height: 4px;
}

.text-font--h1-color--text-black_1202-5837 {
  top: 693px;
  left: 296px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
  width: 99px;
  height: 24px;
}

.text-font--h5-color--text-black_1202-5838 {
  top: 697px;
  left: 446px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
  width: 99px;
  height: 24px;
}

.text-48px_1202-5839 {
  top: 582px;
  left: 342px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
  width: 32px;
  height: 4px;
}

.text-40px_1202-5840 {
  top: 582px;
  left: 448px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
  width: 32px;
  height: 4px;
}

.text-40px_1202-5841 {
  top: 582px;
  left: 557px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
  width: 32px;
  height: 4px;
}

.text-40px_1202-5842 {
  top: 582px;
  left: 636px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
  width: 32px;
  height: 4px;
}

.text-55px_1202-5843 {
  top: 582px;
  left: 741px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
  width: 31px;
  height: 4px;
}

.text-24px_1202-5844 {
  top: 582px;
  left: 873px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
  width: 31px;
  height: 4px;
}

.text-corner-radius--8-border-style--solid-border-color--gray-1_1202-6357 {
  top: 748px;
  left: 1573px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
  width: 117px;
  height: 60px;
}

.vector-1_1202-6358 {
  top: 726px;
  left: 340px;
  position: absolute;
  display: flex;
  display: flex;
}

.vector-2_1202-6359 {
  top: 726px;
  left: 492px;
  position: absolute;
  display: flex;
  display: flex;
}

.text-font--h6-color--gray-3_1202-6360 {
  top: 845px;
  left: 315px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
  width: 78px;
  height: 24px;
}

.text-font--button-text-color--link_1202-6361 {
  top: 845px;
  left: 197px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
  width: 99px;
  height: 24px;
}

.text-component--icon-arrow-left_1202-6362 {
  top: 752px;
  left: 45px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
  width: 98px;
  height: 24px;
}

.vector-3_1202-6363 {
  top: 802px;
  left: 340px;
  position: absolute;
  display: flex;
  display: flex;
}

.vector-4_1202-6364 {
  top: 778px;
  left: 233px;
  position: absolute;
  display: flex;
  display: flex;
}

.vector-20_1202-6365 {
  top: 764px;
  left: 197px;
  position: absolute;
  display: flex;
  display: flex;
}

.demographics_1238-2342 {
  top: 464px;
  left: 112px;
  position: absolute;
  display: flex;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 20px;
  box-sizing: border-box;
  width: 1380px;
}
.table_1238-2343 {
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
  border-radius: 8px;
  border-color: #f1f5f7;
  border-style: solid;
  border-width: 1px;
  background: #ffffff;
  width: -webkit-fill-available;
}
.frame-925_1238-2562 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 48px;
  box-sizing: border-box;
  width: -webkit-fill-available;
}
.frame-927_1238-2563 {
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 48px;
  box-sizing: border-box;
}
.frame-928_1238-2564 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 36px;
  box-sizing: border-box;
}
.back-to-home_1238-2565 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 8px;
  box-sizing: border-box;
}
.icon-arrow_1238-2566 {
  position: relative;
  box-sizing: border-box;
  overflow: hidden;
  display: flex;
}
.vector_1238-2567 {
  top: 5px;
  left: 4px;
  position: absolute;
  display: flex;
  display: flex;
}

.text-text_1238-2568 {
  color: #0071bc;
  font-weight: 500;
  text-align: left;
  text-wrap: nowrap;
}

.member-id_1238-2569 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: -4px;
  box-sizing: border-box;
}
.text-55820474_1238-2570 {
  color: #17181a;
  font-size: 20px;
  font-family: Urbane;
  line-height: 160.0000023841858%;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 600;
  text-align: left;
  text-wrap: wrap;
  width: -webkit-fill-available;
}

.text-member-id_1238-2571 {
  color: #547996;
  font-weight: 500;
  text-align: left;
  text-wrap: wrap;
  width: -webkit-fill-available;
}

.frame-922_1238-2572 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 55px;
  box-sizing: border-box;
}
.frame-924_1238-2573 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 40px;
  box-sizing: border-box;
}
.frame-916_1238-2574 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: -4px;
  box-sizing: border-box;
}
.text-john-dey_1238-2575 {
  color: #17181a;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 600;
  text-align: left;
  text-wrap: wrap;
  width: -webkit-fill-available;
}

.text-member_1238-2576 {
  color: #547996;
  font-weight: 500;
  text-align: left;
  text-wrap: wrap;
  width: -webkit-fill-available;
}

.frame-917_1238-2577 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: -4px;
  box-sizing: border-box;
  width: 69px;
}
.text-01-05-1972_1238-2578 {
  color: #17181a;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 600;
  text-align: left;
  text-wrap: wrap;
  width: -webkit-fill-available;
}

.text-dob_1238-2579 {
  color: #547996;
  font-weight: 500;
  text-align: left;
  text-wrap: wrap;
  width: -webkit-fill-available;
}

.frame-918_1238-2580 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: -4px;
  box-sizing: border-box;
}
.text-m_1238-2581 {
  color: #17181a;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 600;
  text-align: left;
  text-wrap: wrap;
  width: -webkit-fill-available;
}

.text-gender_1238-2582 {
  color: #547996;
  font-weight: 500;
  text-align: left;
  text-wrap: nowrap;
}

.frame-919_1238-2583 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: -4px;
  box-sizing: border-box;
  width: 51px;
}
.text-mahmo_1238-2584 {
  color: #17181a;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 600;
  text-align: left;
  text-wrap: wrap;
  width: -webkit-fill-available;
}

.text-lob_1238-2585 {
  color: #547996;
  font-weight: 500;
  text-align: left;
  text-wrap: wrap;
  width: -webkit-fill-available;
}

.frame-923_1238-2586 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 24px;
  box-sizing: border-box;
}
.frame-921_1238-2587 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: -4px;
  box-sizing: border-box;
}
.text-nicolas-dejong_1238-2588 {
  color: #17181a;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 600;
  text-align: left;
  text-wrap: wrap;
  width: -webkit-fill-available;
}

.text-provider_1238-2589 {
  color: #547996;
  font-weight: 500;
  text-align: left;
  text-wrap: wrap;
  width: -webkit-fill-available;
}

.frame-920_1238-2590 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: -4px;
  box-sizing: border-box;
  width: 68px;
}
.text-882716229_1238-2591 {
  color: #17181a;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 600;
  text-align: left;
  text-wrap: wrap;
  width: -webkit-fill-available;
}

.text-npi_1238-2592 {
  color: #547996;
  font-weight: 500;
  text-align: left;
  text-wrap: wrap;
  width: -webkit-fill-available;
}

