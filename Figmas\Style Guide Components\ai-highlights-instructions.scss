.ai-highlights-v3_924-1847 {
  position: relative;
  box-sizing: border-box;
  overflow: hidden;
  background: #ffffff;
  width: 1094px;
  height: 2233px;
}
.rectangle-291_924-1848 {
  top: 276px;
  left: 225px;
  position: absolute;
  display: flex;
  display: flex;
}

.rectangle-316_929-2536 {
  top: 1063px;
  left: 225px;
  position: absolute;
  display: flex;
  display: flex;
}

.rectangle-314_929-2438 {
  top: 642px;
  left: 228px;
  position: absolute;
  display: flex;
  display: flex;
}

.hits_929-2681 {
  padding: 20px;
  top: 297px;
  left: 244px;
  position: absolute;
  display: flex;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
  border-radius: 8px;
  border-color: #f1f5f7;
  border-style: solid;
  border-width: 1px;
  background: #ffffff;
}
.frame-913_924-2187 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-end;
  gap: 0px;
  box-sizing: border-box;
  width: -webkit-fill-available;
}
.head_924-2188 {
  padding: 0px 0px 12px 0px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 20px;
  box-sizing: border-box;
  width: -webkit-fill-available;
}
.stack_924-2189 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 10px;
  box-sizing: border-box;
}
.text-label_924-2190 {
  color: #17181a;
  font-weight: 600;
  text-align: left;
  text-wrap: nowrap;
}

.table_924-2191 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
  width: -webkit-fill-available;
}
.columns_924-2192 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
  width: -webkit-fill-available;
}
.column_924-2193 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
}
.header-item_924-2194 {
  padding: 0px 8px 0px 8px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 10px;
  box-sizing: border-box;
  border-color: #f1f5f7;
  border-style: solid;
  border-bottom-width: 1px;
  height: 40px;
  width: -webkit-fill-available;
}
.table-item_924-2195 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 40px;
}
.text-label_924-2197 {
  color: #17181a;
  font-weight: 500;
  text-align: left;
  text-wrap: nowrap;
}

.table-item_924-2198 {
  padding: 10px 8px 10px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff3f;
  height: 40px;
  width: -webkit-fill-available;
}
.icon-text_924-2199 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
}
.text-label_924-2200 {
  color: #17181a;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.table-item_924-2201 {
  padding: 10px 8px 10px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
  height: 40px;
  width: -webkit-fill-available;
}
.icon-text_924-2202 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
}
.text-label_924-2203 {
  color: #17181a;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.table-item_924-2204 {
  padding: 10px 8px 10px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
  height: 40px;
  width: -webkit-fill-available;
}
.icon-text_924-2205 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
}
.text-label_924-2206 {
  color: #17181a;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.column_1202-8084 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
}
.header-item_1202-8085 {
  padding: 0px 8px 0px 8px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 10px;
  box-sizing: border-box;
  border-color: #f1f5f7;
  border-style: solid;
  border-bottom-width: 1px;
  height: 40px;
  width: -webkit-fill-available;
}
.table-item_1202-8086 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 40px;
}
.text-label_1202-8088 {
  color: #17181a;
  font-weight: 500;
  text-align: left;
  text-wrap: nowrap;
}

.table-item_1202-8089 {
  padding: 10px 8px 10px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff3f;
  height: 40px;
  width: -webkit-fill-available;
}
.icon-text_1202-8090 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
}
.text-label_1202-8091 {
  color: #17181a;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.table-item_1202-8092 {
  padding: 10px 8px 10px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
  height: 40px;
  width: -webkit-fill-available;
}
.icon-text_1202-8093 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
}
.text-label_1202-8094 {
  color: #17181a;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.table-item_1202-8095 {
  padding: 10px 8px 10px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
  height: 40px;
  width: -webkit-fill-available;
}
.icon-text_1202-8096 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
}
.text-label_1202-8097 {
  color: #17181a;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.column_924-2207 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
}
.header-item_924-2208 {
  padding: 0px 8px 0px 8px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 10px;
  box-sizing: border-box;
  border-color: #f1f5f7;
  border-style: solid;
  border-bottom-width: 1px;
  height: 40px;
}
.table-item_924-2209 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 40px;
}
.text-label_924-2211 {
  color: #17181a;
  font-weight: 500;
  text-align: left;
  text-wrap: nowrap;
}

.table-item_924-2212 {
  padding: 10px 8px 10px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff3f;
  height: 40px;
  width: -webkit-fill-available;
}
.icon-text_924-2213 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
}
.text-label_924-2214 {
  color: #17181a;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.table-item_924-2215 {
  padding: 10px 8px 10px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
  height: 40px;
  width: -webkit-fill-available;
}
.icon-text_924-2216 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
  width: 22px;
}
.text-label_924-2217 {
  color: #17181a;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 72px;
}

.table-item_924-2218 {
  padding: 10px 8px 10px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
  height: 40px;
  width: -webkit-fill-available;
}
.icon-text_924-2219 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
  width: 22px;
}
.text-label_924-2220 {
  color: #17181a;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 72px;
}

.column_924-2221 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
}
.header-item_924-2222 {
  padding: 0px 8px 0px 8px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 10px;
  box-sizing: border-box;
  border-color: #f1f5f7;
  border-style: solid;
  border-bottom-width: 1px;
  height: 40px;
}
.table-item_924-2223 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 40px;
}
.text-label_924-2225 {
  color: #17181a;
  font-weight: 500;
  text-align: left;
  text-wrap: nowrap;
}

.table-item_924-2226 {
  padding: 10px 8px 10px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff3f;
  height: 40px;
  width: -webkit-fill-available;
}
.icon-text_924-2227 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
}
.text-label_924-2228 {
  color: #17181a;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.table-item_924-2229 {
  padding: 10px 8px 10px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
  height: 40px;
  width: -webkit-fill-available;
}
.icon-text_924-2230 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
}
.text-label_924-2231 {
  color: #17181a;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 16px;
}

.table-item_924-2232 {
  padding: 10px 8px 10px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
  height: 40px;
  width: -webkit-fill-available;
}
.icon-text_924-2233 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
}
.text-label_924-2234 {
  color: #17181a;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.column_924-2235 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
}
.header-item_924-2236 {
  padding: 0px 8px 0px 8px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 10px;
  box-sizing: border-box;
  border-color: #f1f5f7;
  border-style: solid;
  border-bottom-width: 1px;
  height: 40px;
  width: -webkit-fill-available;
}
.table-item_924-2237 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 40px;
}
.text-label_924-2239 {
  color: #17181a;
  font-weight: 500;
  text-align: left;
  text-wrap: nowrap;
}

.table-item_924-2240 {
  padding: 10px 8px 10px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff3f;
  height: 40px;
  width: -webkit-fill-available;
}
.icon-text_924-2241 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
}
.text-label_924-2242 {
  color: #0071bc;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.table-item_924-2243 {
  padding: 10px 8px 10px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
  height: 40px;
  width: -webkit-fill-available;
}
.icon-text_924-2244 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
}
.text-label_924-2245 {
  color: #0071bc;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.table-item_924-2246 {
  padding: 10px 8px 10px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
  height: 40px;
  width: -webkit-fill-available;
}
.icon-text_924-2247 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
}
.text-label_924-2248 {
  color: #0071bc;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.column_924-2266 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
  width: -webkit-fill-available;
}
.column_924-2267 {
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex-wrap: nowrap;
  align-items: center;
  gap: 0px;
  box-sizing: border-box;
  width: -webkit-fill-available;
}
.header-item_924-2268 {
  padding: 0px 8px 0px 8px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 10px;
  box-sizing: border-box;
  border-color: #f1f5f7;
  border-style: solid;
  border-bottom-width: 1px;
  width: -webkit-fill-available;
}
.table-item_924-2269 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 40px;
  width: -webkit-fill-available;
}
.text-label_924-2270 {
  color: #17181a;
  font-weight: 500;
  text-align: left;
  text-wrap: wrap;
  width: 54px;
}

.table-item_924-2272 {
  padding: 18px 8px 18px 8px;
  display: flex;
  flex-direction: row;
  justify-content: center;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
  height: 40px;
  width: -webkit-fill-available;
}
.check_924-2273 {
  position: relative;
  width: 16px;
  height: 16px;
}

.table-item_924-2274 {
  padding: 0px 8px 0px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 40px;
}
.check_924-2275 {
  position: relative;
  width: 16px;
  height: 16px;
}

.table-item_924-2276 {
  padding: 0px 8px 0px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 40px;
}
.check_924-2277 {
  position: relative;
  width: 16px;
  height: 16px;
}

.hits_1238-3232 {
  padding: 20px;
  top: 1805px;
  left: 61px;
  position: absolute;
  display: flex;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
  border-radius: 8px;
  border-color: #f1f5f7;
  border-style: solid;
  border-width: 1px;
  background: #ffffff;
  width: 447px;
}
.frame-913_1238-3233 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-end;
  gap: 0px;
  box-sizing: border-box;
  width: -webkit-fill-available;
}
.head_1238-3234 {
  padding: 0px 0px 12px 0px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 20px;
  box-sizing: border-box;
  width: -webkit-fill-available;
}
.stack_1238-3235 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 10px;
  box-sizing: border-box;
}
.text-label_1238-3236 {
  color: #17181a;
  font-weight: 600;
  text-align: left;
  text-wrap: nowrap;
}

.table_1238-3237 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
  width: -webkit-fill-available;
}
.columns_1238-3238 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
  width: -webkit-fill-available;
}
.column_1238-3239 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
}
.header-item_1238-3240 {
  padding: 0px 8px 0px 8px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 10px;
  box-sizing: border-box;
  border-color: #f1f5f7;
  border-style: solid;
  border-bottom-width: 1px;
  height: 40px;
  width: -webkit-fill-available;
}
.table-item_1238-3241 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 40px;
}
.text-label_1238-3243 {
  color: #17181a;
  font-weight: 500;
  text-align: left;
  text-wrap: nowrap;
}

.table-item_1238-3244 {
  padding: 10px 8px 10px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff3f;
  height: 40px;
  width: -webkit-fill-available;
}
.icon-text_1238-3245 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
}
.text-label_1238-3246 {
  color: #17181a;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.table-item_1238-3247 {
  padding: 10px 8px 10px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
  height: 40px;
  width: -webkit-fill-available;
}
.icon-text_1238-3248 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
}
.text-label_1238-3249 {
  color: #17181a;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.table-item_1238-3250 {
  padding: 10px 8px 10px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
  height: 40px;
  width: -webkit-fill-available;
}
.icon-text_1238-3251 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
}
.text-label_1238-3252 {
  color: #17181a;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.column_1238-3253 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
}
.header-item_1238-3254 {
  padding: 0px 8px 0px 8px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 10px;
  box-sizing: border-box;
  border-color: #f1f5f7;
  border-style: solid;
  border-bottom-width: 1px;
  height: 40px;
  width: -webkit-fill-available;
}
.table-item_1238-3255 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 40px;
}
.text-label_1238-3257 {
  color: #17181a;
  font-weight: 500;
  text-align: left;
  text-wrap: nowrap;
}

.table-item_1238-3258 {
  padding: 10px 8px 10px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff3f;
  height: 40px;
  width: -webkit-fill-available;
}
.icon-text_1238-3259 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
}
.text-label_1238-3260 {
  color: #17181a;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.table-item_1238-3261 {
  padding: 10px 8px 10px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
  height: 40px;
  width: -webkit-fill-available;
}
.icon-text_1238-3262 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
}
.text-label_1238-3263 {
  color: #17181a;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.table-item_1238-3264 {
  padding: 10px 8px 10px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
  height: 40px;
  width: -webkit-fill-available;
}
.icon-text_1238-3265 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
}
.text-label_1238-3266 {
  color: #17181a;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.column_1238-3267 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
}
.header-item_1238-3268 {
  padding: 0px 8px 0px 8px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 10px;
  box-sizing: border-box;
  border-color: #f1f5f7;
  border-style: solid;
  border-bottom-width: 1px;
  height: 40px;
}
.table-item_1238-3269 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 40px;
}
.text-label_1238-3271 {
  color: #17181a;
  font-weight: 500;
  text-align: left;
  text-wrap: nowrap;
}

.table-item_1238-3272 {
  padding: 10px 8px 10px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff3f;
  height: 40px;
  width: -webkit-fill-available;
}
.icon-text_1238-3273 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
}
.text-label_1238-3274 {
  color: #17181a;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.table-item_1238-3275 {
  padding: 10px 8px 10px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
  height: 40px;
  width: -webkit-fill-available;
}
.icon-text_1238-3276 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
  width: 22px;
}
.text-label_1238-3277 {
  color: #17181a;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 72px;
}

.table-item_1238-3278 {
  padding: 10px 8px 10px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
  height: 40px;
  width: -webkit-fill-available;
}
.icon-text_1238-3279 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
  width: 22px;
}
.text-label_1238-3280 {
  color: #17181a;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 72px;
}

.column_1238-3281 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
}
.header-item_1238-3282 {
  padding: 0px 8px 0px 8px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 10px;
  box-sizing: border-box;
  border-color: #f1f5f7;
  border-style: solid;
  border-bottom-width: 1px;
  height: 40px;
}
.table-item_1238-3283 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 40px;
}
.text-label_1238-3285 {
  color: #17181a;
  font-weight: 500;
  text-align: left;
  text-wrap: nowrap;
}

.table-item_1238-3286 {
  padding: 10px 8px 10px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff3f;
  height: 40px;
  width: -webkit-fill-available;
}
.icon-text_1238-3287 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
}
.text-label_1238-3288 {
  color: #17181a;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.table-item_1238-3289 {
  padding: 10px 8px 10px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
  height: 40px;
  width: -webkit-fill-available;
}
.icon-text_1238-3290 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
}
.text-label_1238-3291 {
  color: #17181a;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 16px;
}

.table-item_1238-3292 {
  padding: 10px 8px 10px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
  height: 40px;
  width: -webkit-fill-available;
}
.icon-text_1238-3293 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
}
.text-label_1238-3294 {
  color: #17181a;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.column_1238-3295 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
}
.header-item_1238-3296 {
  padding: 0px 8px 0px 8px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 10px;
  box-sizing: border-box;
  border-color: #f1f5f7;
  border-style: solid;
  border-bottom-width: 1px;
  height: 40px;
  width: -webkit-fill-available;
}
.table-item_1238-3297 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 40px;
}
.text-label_1238-3299 {
  color: #17181a;
  font-weight: 500;
  text-align: left;
  text-wrap: nowrap;
}

.table-item_1238-3300 {
  padding: 10px 8px 10px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff3f;
  height: 40px;
  width: -webkit-fill-available;
}
.icon-text_1238-3301 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
}
.text-label_1238-3302 {
  color: #0071bc;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.table-item_1238-3303 {
  padding: 10px 8px 10px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
  height: 40px;
  width: -webkit-fill-available;
}
.icon-text_1238-3304 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
}
.text-label_1238-3305 {
  color: #0071bc;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.table-item_1238-3306 {
  padding: 10px 8px 10px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
  height: 40px;
  width: -webkit-fill-available;
}
.icon-text_1238-3307 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
}
.text-label_1238-3308 {
  color: #0071bc;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.column_1238-3309 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
  width: -webkit-fill-available;
}
.column_1238-3310 {
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex-wrap: nowrap;
  align-items: center;
  gap: 0px;
  box-sizing: border-box;
  width: -webkit-fill-available;
}
.header-item_1238-3311 {
  padding: 0px 8px 0px 8px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 10px;
  box-sizing: border-box;
  border-color: #f1f5f7;
  border-style: solid;
  border-bottom-width: 1px;
  width: -webkit-fill-available;
}
.table-item_1238-3312 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 40px;
  width: -webkit-fill-available;
}
.text-label_1238-3313 {
  color: #17181a;
  font-weight: 500;
  text-align: left;
  text-wrap: wrap;
  width: 54px;
}

.table-item_1238-3315 {
  padding: 18px 8px 18px 8px;
  display: flex;
  flex-direction: row;
  justify-content: center;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
  height: 40px;
  width: -webkit-fill-available;
}
.check_1238-3316 {
  position: relative;
  width: 16px;
  height: 16px;
}

.table-item_1238-3317 {
  padding: 0px 8px 0px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 40px;
}
.check_1238-3318 {
  position: relative;
  width: 16px;
  height: 16px;
}

.table-item_1238-3319 {
  padding: 0px 8px 0px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 40px;
}
.check_1238-3320 {
  position: relative;
  width: 16px;
  height: 16px;
}

.hits_1204-13118 {
  top: 1084px;
  left: 251px;
  position: absolute;
  display: flex;
  width: 447px;
}

.hits_1204-12782 {
  top: 663px;
  left: 247px;
  position: absolute;
  display: flex;
  width: 447px;
}

.text-spacing-_924-1851 {
  top: 606px;
  left: 64px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 18px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 600;
  text-align: left;
  width: 83px;
  height: 16px;
}

.text-styling-_924-1852 {
  top: 1014px;
  left: 62px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 18px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 600;
  text-align: left;
  width: 83px;
  height: 16px;
}

.text-auto-populate-interaction-_1241-3015 {
  top: 1712px;
  left: 62px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 18px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 600;
  text-align: left;
  width: 260px;
  height: 16px;
}

.text-behavior--scrolls-with-page--interactive-in-both-default-and-entered-state--categories-change-based-on-the-measure-_924-1853 {
  top: 170px;
  left: 62px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 18px;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  text-align: left;
  width: 1040px;
  height: 34px;
}

.text-sorting--entries-are-sorted-from-oldest--bottom--to-newest--top--date-of-service-_924-1854 {
  top: 220px;
  left: 62px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 18px;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  text-align: left;
  width: 1040px;
  height: 16px;
}

.text-usage--the-container-where-the-genai-tool-s-identifications-are-housed-_924-1855 {
  top: 132px;
  left: 63px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 18px;
  letter-spacing: 0%;
  text-decoration: none;
  text-align: left;
  text-wrap: wrap;
  width: 1478px;
  height: 22px;
}

.text-ai-highlights--hits--container_924-1856 {
  top: 63px;
  left: 62px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 36px;
  font-family: Urbane;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 600;
  text-align: left;
  text-wrap: nowrap;
  width: 542px;
  height: 43px;
}

.vector_924-1857 {
  top: 927px;
  left: 247px;
  position: absolute;
  display: flex;
  display: flex;
}

.vector_924-1858 {
  top: 887px;
  left: 225px;
  position: absolute;
  display: flex;
  display: flex;
}

.text-20px_924-1859 {
  top: 934px;
  left: 242px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
  width: 32px;
  height: 4px;
}

.text-20px_924-1860 {
  top: 897px;
  left: 191px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
  width: 32px;
  height: 4px;
}

.text-corner-radius--8-border-style--solid-border-color--gray-1_924-1861 {
  top: 1070px;
  left: 731px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
  width: 117px;
  height: 60px;
}

.text-default--not-selected_924-2039 {
  top: 419px;
  left: 725px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
  width: 125px;
  height: 4px;
}

.text-component--check-align--align-center_924-2040 {
  top: 1246px;
  left: 730px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
  width: 116px;
  height: 24px;
}

.text-header-item-stroke--bottom-size--1px-color--gray-1_924-2041 {
  top: 1182px;
  left: 730px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
  width: 171px;
  height: 44px;
}

.text-font--body-color--link-text-decoration--underline_924-2042 {
  top: 1196px;
  left: 131px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 104px;
  height: 64px;
}

.text-font--body-color--text-black_924-2043 {
  top: 1282px;
  left: 131px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
  width: 99px;
  height: 24px;
}

.text-font--h1-color--text-black_924-2044 {
  top: 1100px;
  left: 131px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
  width: 99px;
  height: 24px;
}

.text-font--h3-color--text-black_924-2045 {
  top: 1149px;
  left: 131px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
  width: 99px;
  height: 24px;
}

.vector-15_924-2046 {
  top: 438px;
  left: 235px;
  position: absolute;
  display: flex;
  display: flex;
}

.vector-21_924-2047 {
  top: 1225px;
  left: 235px;
  position: absolute;
  display: flex;
  display: flex;
}

.vector-24_924-2048 {
  top: 1285px;
  left: 235px;
  position: absolute;
  display: flex;
  display: flex;
}

.vector-25_924-2049 {
  top: 1121px;
  left: 235px;
  position: absolute;
  display: flex;
  display: flex;
}

.vector-26_924-2050 {
  top: 1170px;
  left: 235px;
  position: absolute;
  display: flex;
  display: flex;
}

.vector-16_924-2051 {
  top: 438px;
  left: 428px;
  position: absolute;
  display: flex;
  display: flex;
}

.vector-22_924-2052 {
  top: 1225px;
  left: 562px;
  position: absolute;
  display: flex;
  display: flex;
}

.text-clicking-page-number-links-to-section-of-page-where-the-corresponding-identification-is_924-2053 {
  top: 429px;
  left: 63px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  width: 167px;
  height: 72px;
}

.vector-14_924-2054 {
  top: 419px;
  left: 684px;
  position: absolute;
  display: flex;
  display: flex;
}

.vector-23_924-2055 {
  top: 1248px;
  left: 687px;
  position: absolute;
  display: flex;
  display: flex;
}

.vector-27_924-2056 {
  top: 1186px;
  left: 684px;
  position: absolute;
  display: flex;
  display: flex;
}

.rectangle-293_924-2057 {
  top: 683px;
  left: 246px;
  position: absolute;
  display: flex;
  display: flex;
}

.rectangle-294_924-2058 {
  top: 907px;
  left: 246px;
  position: absolute;
  display: flex;
  display: flex;
}

.rectangle-295_924-2059 {
  top: 907px;
  left: 267px;
  position: absolute;
  display: flex;
  display: flex;
}

.rectangle-296_924-2060 {
  top: 907px;
  left: 694px;
  position: absolute;
  display: flex;
  display: flex;
}

.rectangle-297_924-2061 {
  top: 727px;
  left: 266px;
  position: absolute;
  display: flex;
  display: flex;
}

.rectangle-298_924-2062 {
  top: 737px;
  left: 266px;
  position: absolute;
  display: flex;
  display: flex;
}

.rectangle-299_924-2063 {
  top: 767px;
  left: 266px;
  position: absolute;
  display: flex;
  display: flex;
}

.rectangle-312_924-2064 {
  top: 887px;
  left: 266px;
  position: absolute;
  display: flex;
  display: flex;
}

.rectangle-300_924-2065 {
  top: 727px;
  left: 267px;
  position: absolute;
  display: flex;
  display: flex;
}

.rectangle-301_924-2066 {
  top: 727px;
  left: 330px;
  position: absolute;
  display: flex;
  display: flex;
}

.rectangle-302_924-2067 {
  top: 727px;
  left: 338px;
  position: absolute;
  display: flex;
  display: flex;
}

.rectangle-303_924-2068 {
  top: 727px;
  left: 399px;
  position: absolute;
  display: flex;
  display: flex;
}

.rectangle-304_924-2069 {
  top: 727px;
  left: 407px;
  position: absolute;
  display: flex;
  display: flex;
}

.rectangle-305_924-2070 {
  top: 727px;
  left: 465px;
  position: absolute;
  display: flex;
  display: flex;
}

.rectangle-306_924-2071 {
  top: 727px;
  left: 473px;
  position: absolute;
  display: flex;
  display: flex;
}

.rectangle-307_924-2072 {
  top: 727px;
  left: 535px;
  position: absolute;
  display: flex;
  display: flex;
}

.rectangle-318_1204-12875 {
  top: 727px;
  left: 584px;
  position: absolute;
  display: flex;
  display: flex;
}

.rectangle-317_1204-12874 {
  top: 727px;
  left: 543px;
  position: absolute;
  display: flex;
  display: flex;
}

.rectangle-319_1204-12876 {
  top: 727px;
  left: 592px;
  position: absolute;
  display: flex;
  display: flex;
}

.rectangle-320_1204-12877 {
  top: 727px;
  left: 666px;
  position: absolute;
  display: flex;
  display: flex;
}

.vector_924-2077 {
  top: 715px;
  left: 245px;
  position: absolute;
  display: flex;
  display: flex;
}

.vector_924-2078 {
  top: 727px;
  left: 245px;
  position: absolute;
  display: flex;
  display: flex;
}

.vector_924-2079 {
  top: 757px;
  left: 245px;
  position: absolute;
  display: flex;
  display: flex;
}

.vector_924-2080 {
  top: 877px;
  left: 245px;
  position: absolute;
  display: flex;
  display: flex;
}

.text-12px_924-2081 {
  top: 719px;
  left: 214px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
  width: 27px;
  height: 4px;
}

.text-10px_924-2082 {
  top: 732px;
  left: 214px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
  width: 28px;
  height: 4px;
}

.text-10px_924-2083 {
  top: 762px;
  left: 214px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
  width: 28px;
  height: 4px;
}

.text-10px_924-2084 {
  top: 882px;
  left: 214px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
  width: 28px;
  height: 4px;
}

.vector_924-2085 {
  top: 909px;
  left: 329px;
  position: absolute;
  display: flex;
  display: flex;
}

.vector_924-2086 {
  top: 909px;
  left: 338px;
  position: absolute;
  display: flex;
  display: flex;
}

.text-8px_924-2087 {
  top: 914px;
  left: 312px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
  width: 23px;
  height: 4px;
}

.text-8px_924-2088 {
  top: 914px;
  left: 339px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
  width: 23px;
  height: 4px;
}

.text-all-table-items-are-align-left-except-for-checkboxes--which-are-align-center_924-2089 {
  top: 765px;
  left: 742px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 169px;
  height: 44px;
}

.hits_1010-4438 {
  top: 1421px;
  left: 242px;
  position: absolute;
  display: flex;
  width: 447px;
}

.text-when-user-has-clicked-a-page--the-entire-row-background-color-changes-to-secondary-blue_1180-576 {
  top: 1539px;
  left: 724px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 242px;
  height: 44px;
}

.vector-31_1180-577 {
  top: 1542px;
  left: 678px;
  position: absolute;
  display: flex;
  display: flex;
}

.notes_1241-2942 {
  padding: 20px;
  top: 1805px;
  left: 553px;
  position: absolute;
  display: flex;
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
  border-radius: 8px;
  border-color: #f1f5f7;
  border-style: solid;
  border-width: 1px;
  background: #ffffff;
  width: 447px;
}
.head_1241-2943 {
  padding: 0px 0px 12px 0px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 20px;
  box-sizing: border-box;
  width: -webkit-fill-available;
}
.frame-929_1241-2944 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 12px;
  box-sizing: border-box;
  width: -webkit-fill-available;
}
.stack_1241-2945 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 10px;
  box-sizing: border-box;
  width: -webkit-fill-available;
}
.text-label_1241-2946 {
  color: #17181a;
  font-weight: 600;
  text-align: left;
  text-wrap: nowrap;
}

.right-corner_1241-2947 {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  width: -webkit-fill-available;
  height: 100%;
}

.menu_1241-2948 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
  height: 41px;
  width: -webkit-fill-available;
}
.second-menu_1241-2949 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 0px;
  box-sizing: content-box;
  border-color: #d9e1e7;
  border-style: solid;
  border-bottom-width: 1px;
  background: #ffffff;
  width: -webkit-fill-available;
}
.bradcrumb_1241-2950 {
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
  height: 40px;
}
.box_1241-2951 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 0px;
  box-sizing: border-box;
  height: 100%;
}
.menu_1241-2952 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 16px;
  box-sizing: border-box;
  height: 100%;
}
.menu-item_1241-2953 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 16px;
  box-sizing: border-box;
  border-color: #3870b8;
  border-style: solid;
  border-bottom-width: 1px;
  height: 100%;
}
.menu-item_1241-2954 {
  padding: 4px 8px 4px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 100%;
}
.frame-831_1241-2955 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  border-radius: 6px;
}
.text-label_1241-2957 {
  color: #3870b8;
  font-weight: 600;
  text-align: left;
  text-wrap: nowrap;
}

.menu-item-_1241-2959 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 16px;
  box-sizing: border-box;
  height: 100%;
}
.menu-item_1241-2960 {
  padding: 4px 8px 4px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 100%;
}
.frame-831_1241-2961 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  border-radius: 6px;
}
.text-label_1241-2963 {
  color: #547996;
  font-weight: 600;
  text-align: left;
  text-wrap: nowrap;
}

.menu-item_1241-2965 {
  padding: 4px 8px 4px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 100%;
}
.frame-831_1241-2966 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  border-radius: 6px;
}
.text-label_1241-2968 {
  color: #547996;
  font-weight: 600;
  text-align: left;
  text-wrap: nowrap;
}

.frame-916_1241-2970 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 12px;
  box-sizing: border-box;
  width: -webkit-fill-available;
}
.frame-942_1241-2971 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 8px;
  box-sizing: border-box;
}
.check_1241-2972 {
  position: relative;
  width: 16px;
  height: 16px;
}

.text-teleheath_1241-2973 {
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.frame-936_1241-2974 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 4px;
  box-sizing: border-box;
  width: -webkit-fill-available;
}
.frame-939_1241-2975 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 4px;
  box-sizing: border-box;
  width: -webkit-fill-available;
}
.frame-938_1241-2976 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 4px;
  box-sizing: border-box;
}
.text-systolic_1241-2977 {
  color: #547996;
  font-size: 10px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.form-field_1241-2978 {
  width: 112px;
}

.frame-939_1241-2979 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 4px;
  box-sizing: border-box;
}
.text-diastolic_1241-2980 {
  color: #547996;
  font-size: 10px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.form-field_1241-2981 {
  width: 112px;
}

.frame-937_1241-2982 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 4px;
  box-sizing: border-box;
  width: -webkit-fill-available;
}
.text-date-of-service_1241-2983 {
  color: #547996;
  font-size: 10px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.calendar_1241-2984 {
  height: 48px;
  width: -webkit-fill-available;
}

.frame-938_1241-2985 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 4px;
  box-sizing: border-box;
  width: -webkit-fill-available;
}
.text-notes_1241-2986 {
  color: #547996;
  font-size: 10px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.notes_1241-2987 {
  width: -webkit-fill-available;
}

.arrow-5_1241-3013 {
  top: 1927px;
  left: 512px;
  position: absolute;
  display: flex;
  display: flex;
}

.text-behavior--when-the-include-checkbox-is-selected--the-corresponding-values-auto-populate-in-the-results-section--and-the-fields-that-are-auto-populated-switch-to-the-active-state-until-another-part-of-the-page-is-clicked-on-_1241-3014 {
  top: 1740px;
  left: 62px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 938px;
  height: 40px;
}
