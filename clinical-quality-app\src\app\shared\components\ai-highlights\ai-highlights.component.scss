@use 'variables' as variables;
@use 'mixins' as mix;


.ai-highlights-container {
  padding: 20px;
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #f1f5f7;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  box-sizing: border-box;
  width: 100%; // Responsive: fill parent
  min-width: 0;
  max-width: 100%;
}

.ai-highlights__header {
  padding: 0 0 12px 0;
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 20px;
  width: 100%;
  box-sizing: border-box;
}

.ai-highlights__title {
  color: #17181a;
  font-family: 'Urbane', 'Segoe UI', Arial, sans-serif;
  font-size: 20px;
  font-weight: 600;
  line-height: 32px;
  white-space: nowrap;
}

.ai-highlights__table {
  display: flex;
  flex-direction: column; /* Stack rows vertically */
  align-items: flex-start;
  width: 100%;
  box-sizing: border-box;
}

.ai-highlights__columns {
  display: flex;
  flex-direction: row; /* Layout columns horizontally */
  align-items: flex-start;
  width: 100%;
  box-sizing: border-box;
}

// Responsive columns: use flex-basis and min-width for each column
.ai-highlights__column {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  box-sizing: border-box;
  flex: 1 1 0;
  min-width: 60px;
}
.ai-highlights__column--measure { flex-basis: 80px; min-width: 70px; max-width: 160px; }
.ai-highlights__column--dos { flex-basis: 80px; min-width: 70px; max-width: 120px; }
.ai-highlights__column--systolic { flex-basis: 60px; min-width: 50px; max-width: 90px; }
.ai-highlights__column--diastolic { flex-basis: 60px; min-width: 50px; max-width: 90px; }
.ai-highlights__column--page { flex-basis: 60px; min-width: 50px; max-width: 90px; }
.ai-highlights__column--include { flex-basis: 40px; min-width: 36px; max-width: 60px; align-items: center; }

// Remove width: 100% from header and row items
.ai-highlights__header-item,
.ai-highlights__row {
  width: auto;
}

.ai-highlights__header-item {
  padding: 0 8px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  gap: 10px;
  border-bottom: 1px solid #f1f5f7;
  height: 40px;
  box-sizing: border-box;
  width: 100%;
}

.ai-highlights__header-label {
  color: #17181a;
  font-family: 'Urbane', 'Segoe UI', Arial, sans-serif;
  font-size: 12px;
  font-weight: 500;
  line-height: 20px;
  white-space: nowrap;
}

.ai-highlights__row {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 40px;
  /* width: 100%; Removed as it's a flex item within a column container */
  padding: 10px 8px;
  background: #ffffff;
}

.ai-highlights__row--alt {
  background: rgba(191, 208, 238, 0.25); /* secondary-blue */
}

.ai-highlights__cell {
  color: #17181a;
  font-family: 'Urbane', 'Segoe UI', Arial, sans-serif;
  font-size: 12px;
  font-weight: 300;
  line-height: 16px;
  white-space: nowrap;
  flex: 1 1 0; /* Apply flex properties to cells */
}

.ai-highlights__cell--measure { flex-basis: 80px; min-width: 70px; max-width: 160px; }
.ai-highlights__cell--dos { flex-basis: 80px; min-width: 70px; max-width: 120px; }
.ai-highlights__cell--systolic { flex-basis: 60px; min-width: 50px; max-width: 90px; }
.ai-highlights__cell--diastolic { flex-basis: 60px; min-width: 50px; max-width: 90px; }
.ai-highlights__cell--page { flex-basis: 60px; min-width: 50px; max-width: 90px; }
.ai-highlights__cell--include { flex-basis: 40px; min-width: 36px; max-width: 60px; align-items: center; }

.ai-highlights__cell--link {
  color: #0071bc;
  text-decoration: underline;
  cursor: pointer;
}

.ai-highlights__checkbox {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  background: #fff;
  border: 1px solid #d9e1e7;
  border-radius: 5px;
  cursor: pointer;
}

.ai-highlights__checkbox--checked {
  background: #17181a;
  border-color: #17181a;
}

.ai-highlights__checkbox-icon {
  width: 8px;
  height: 8px;
  display: block;
  margin: auto;
}

.ai-highlights__row--selected {
  background: #bfd0ee40 !important;
}

/* Responsive tweaks if needed */
@media (max-width: 900px) {
  .ai-highlights__column--measure,
  .ai-highlights__column--dos,
  .ai-highlights__column--systolic,
  .ai-highlights__column--diastolic,
  .ai-highlights__column--page {
    flex-basis: 60px;
    min-width: 40px;
    max-width: 1fr;
  }
}
@media (max-width: 600px) {
  .ai-highlights-container {
    padding: 10px;
  }
  .ai-highlights__header {
    gap: 10px;
  }
  .ai-highlights__row, .ai-highlights__header-item {
    padding: 6px 4px;
    font-size: 11px;
  }
  .ai-highlights__column {
    min-width: 40px;
  }
}

