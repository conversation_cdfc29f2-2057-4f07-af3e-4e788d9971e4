@use 'variables' as variables;
@use 'mixins' as mix;

// AI Highlights component styles based on Figma hits.scss specifications
.hits {
  padding: 20px; // Exact Figma padding
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
  border-radius: 8px;
  border-color: #f1f5f7;
  border-style: solid;
  border-width: 1px;
  background: #ffffff;
  width: 100%;
}

.frame-913 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-end;
  gap: 0px;
  box-sizing: border-box;
  width: 100%;
}

.head {
  padding: 0px 0px 12px 0px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 20px;
  box-sizing: border-box;
  width: 100%;
}

.stack {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 10px;
  box-sizing: border-box;
}

.text-label {
  color: #17181a;
  font-family: 'Urbane', 'Segoe UI', Arial, sans-serif;
  font-size: 20px;
  font-weight: 600;
  line-height: 32px;
  text-align: left;
  text-wrap: nowrap;
}

.table {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
  width: 100%;
}

.columns {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
  width: 100%;
}

.column {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
}

.header-item {
  border-bottom: 1px solid #f1f5f7;

  .table-item {
    padding: 10px 8px 10px 8px;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    flex-wrap: nowrap;
    align-items: center;
    gap: 12px;
    box-sizing: border-box;
    height: 40px;
    width: 100%;

    .text-label {
      color: #17181a;
      font-family: 'Urbane', 'Segoe UI', Arial, sans-serif;
      font-size: 12px;
      font-weight: 500;
      line-height: 20px;
      text-align: left;
      text-wrap: nowrap;
    }
  }
}

.table-item {
  padding: 10px 8px 10px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
  height: 40px;
  width: 100%;

  &.highlighted {
    background: #bfd0ee40; // Secondary blue for selected row
  }

  .icon-text {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    flex-wrap: nowrap;
    align-items: center;
    gap: 12px;
    box-sizing: border-box;

    .text-label {
      color: #17181a;
      font-family: 'Urbane', 'Segoe UI', Arial, sans-serif;
      font-size: 12px;
      font-weight: 300;
      line-height: 16px;
      text-align: left;
      text-wrap: nowrap;
    }

    .page-link {
      background: none;
      border: none;
      padding: 0;
      cursor: pointer;

      .text-label {
        color: #0071bc;
        text-decoration: underline;
      }
    }
  }

  .check {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
@media (max-width: 600px) {
  .ai-highlights-container {
    padding: 10px;
  }
  .ai-highlights__header {
    gap: 10px;
  }
  .ai-highlights__row, .ai-highlights__header-item {
    padding: 6px 4px;
    font-size: 11px;
  }
  .ai-highlights__column {
    min-width: 40px;
  }
}

