import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { CheckboxComponent } from '../form-controls/checkbox/checkbox.component';

export interface AiHighlightData {
  id: string;
  measure: string;
  dateOfService: string;
  systolic: number;
  diastolic: number;
  page: number;
  include: boolean;
}

@Component({
  selector: 'app-ai-highlights',
  standalone: true,
  imports: [CommonModule, FormsModule, CheckboxComponent],
  templateUrl: './ai-highlights.component.html',
  styleUrls: ['./ai-highlights.component.scss']
})
export class AiHighlightsComponent implements OnInit {
  @Input() title: string = 'AI Highlights';
  @Input() data: AiHighlightData[] = [];
  @Input() showHeader: boolean = true;
  
  // Track the currently selected highlight (the one user clicked to navigate to)
  selectedHighlightId: string | null = null;

  @Output() dataChange = new EventEmitter<AiHighlightData[]>();
  @Output() pageClick = new EventEmitter<{ highlight: AiHighlightData, page: number }>();
  @Output() includeChange = new EventEmitter<{ highlight: AiHighlightData, include: boolean }>();

  ngOnInit(): void {
    if (this.data.length === 0) {
      this.data = this.getDefaultData();
    }

    // No highlight should be selected by default
    this.selectedHighlightId = null;
  }

  onIncludeChange(highlight: AiHighlightData, include: boolean): void {
    highlight.include = include;
    this.includeChange.emit({ highlight, include });
    this.dataChange.emit(this.data);
  }

  onPageClick(highlight: AiHighlightData): void {
    // Set the selected highlight when user clicks on a page link
    this.selectedHighlightId = highlight.id;
    this.pageClick.emit({ highlight, page: highlight.page });
  }

  trackByHighlightId(_index: number, highlight: AiHighlightData): string {
    return highlight.id;
  }

  private getDefaultData(): AiHighlightData[] {
    return [
      {
        id: 'highlight-1',
        measure: 'CBP',
        dateOfService: '07/21/24',
        systolic: 136,
        diastolic: 82,
        page: 2,
        include: false
      },
      {
        id: 'highlight-2',
        measure: 'CBP',
        dateOfService: '07/21/24',
        systolic: 140,
        diastolic: 82,
        page: 2,
        include: false
      },
      {
        id: 'highlight-3',
        measure: 'CBP',
        dateOfService: '05/21/24',
        systolic: 150,
        diastolic: 90,
        page: 7,
        include: false
      }
    ];
  }
}
