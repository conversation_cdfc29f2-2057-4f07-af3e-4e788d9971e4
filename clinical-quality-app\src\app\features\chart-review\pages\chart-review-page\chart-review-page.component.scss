// Import design system variables and mixins
@use 'styles/variables' as variables;
@use 'styles/mixins' as mixins;

.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

// Main Content Layout - responsive width with max constraint
.main-content {
  width: 100%; // Use full available width
  max-width: 1800px; // Increased from 1440px for better screen utilization
  margin: 0 auto; // Center the content
  background: #F6F6F6;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  display: flex;
  padding: 20px 30px;
  gap: 20px;
  box-sizing: border-box;

  // Responsive padding
  @media (max-width: 768px) {
    padding: 15px 20px;
  }

  @media (max-width: 480px) {
    padding: 10px 15px;
  }
}

// Demographics section - constrained to match two-column layout width
.demographics-section {
  width: 100%;
  margin-bottom: 0; // Remove margin, gap is handled by main-content

  // Match the responsive behavior of the content-layout below
  @media (max-width: 1200px) {
    width: 100%; // Full width when columns stack vertically
  }
}

// Override demographics component styling to match content layout constraints
:host ::ng-deep .demographics-section app-demographics .demographics-container {
  // The demographics component should align with the visual boundaries of the content below
  // Since both the PDF column and demographics have 20px padding, they should align naturally
  // But if there's still overflow, we constrain it here
  max-width: 100%;
  box-sizing: border-box;
}

// Two-column layout - responsive with improved breakpoints
.content-layout {
  width: 100%;
  justify-content: flex-start;
  align-items: flex-start;
  gap: 20px;
  display: flex;

  // Enhanced responsive breakpoints
  @media (max-width: 1400px) {
    gap: 15px; // Reduce gap on medium screens
  }

  @media (max-width: 1200px) {
    flex-direction: column;
    gap: 20px;
  }

  @media (max-width: 768px) {
    gap: 15px;
  }
}

// Left Column: PDF Viewer - responsive and flexible
.pdf-column {
  flex: 1; // Takes remaining space after right column
  min-width: 500px; // Reduced minimum width for better responsiveness
  padding: 20px;
  background: white;
  border-radius: 8px;
  outline: 1px #F1F5F7 solid;
  outline-offset: -1px;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  display: flex;

  // Enhanced responsive adjustments
  @media (max-width: 1400px) {
    min-width: 450px;
  }

  @media (max-width: 1200px) {
    flex: none;
    width: 100%;
    min-width: auto;
  }

  @media (max-width: 768px) {
    padding: 15px;
  }

  @media (max-width: 480px) {
    padding: 10px;
  }
}

// Right Column: Hits and Results - responsive width
.right-column {
  width: 517px; // Optimal width from Figma
  max-width: 600px; // Allow slight expansion on larger screens
  min-width: 400px; // Minimum width for usability
  flex-shrink: 0; // Don't shrink below minimum
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-end;
  gap: 20px;
  display: flex;

  // Enhanced responsive adjustments
  @media (max-width: 1600px) {
    width: 500px; // Slightly smaller on medium-large screens
  }

  @media (max-width: 1400px) {
    width: 480px;
    min-width: 380px;
  }

  @media (max-width: 1200px) {
    width: 100%;
    max-width: none;
    min-width: auto;
    align-items: stretch;
  }

  @media (max-width: 768px) {
    gap: 15px;
  }
}

// AI Highlights section - container for ai-highlights component
.ai-highlights-section {
  align-self: stretch;
  display: flex;
  flex-direction: column;
  // Remove padding, background, borders - let the component handle its own styling
}

// Hits section - container for hits component
.hits-section {
  align-self: stretch;
  display: flex;
  flex-direction: column;
  // Remove padding, background, borders - let the component handle its own styling
}

// Results section - container for results component
.results-section {
  align-self: stretch;
  display: flex;
  flex-direction: column;
  // Remove padding, background, borders - let the component handle its own styling
}

// Submit section - exact Figma specs
.submit-section {
  border-radius: 8px;
  justify-content: flex-end;
  align-items: flex-end;
  display: inline-flex;
}

// PDF viewer - enhanced responsive design
:host ::ng-deep app-pdf-viewer {
  align-self: stretch;
  height: 979px; // Optimal height from Figma
  min-height: 600px; // Minimum height for usability
  position: relative;
  background: #E8E8EB;
  overflow: hidden;

  // Enhanced responsive adjustments
  @media (max-width: 1600px) {
    height: 850px; // Slightly smaller on large screens
  }

  @media (max-width: 1400px) {
    height: 750px;
    min-height: 550px;
  }

  @media (max-width: 1200px) {
    height: 70vh; // Use viewport height on smaller screens
    min-height: 500px;
  }

  @media (max-width: 768px) {
    height: 60vh;
    min-height: 400px;
  }

  @media (max-width: 480px) {
    height: 50vh;
    min-height: 350px;
  }
}

:host ::ng-deep .pdf-viewer-container {
  width: 100%;
  height: 100%;
  background: #E8E8EB;
  overflow: auto; // Allow scrolling
}

:host ::ng-deep ngx-extended-pdf-viewer {
  width: 100%;
  height: 100%;
}