Free
Code
Log

view

code

style

tokens
.chart-view---v2_1134-1931 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
  background: #f6f6f6;
  width: 1440px;
}
.screen_1134-1932 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
  background: #f9fbfc;
  width: 1440px;
}
.menu_1134-1933 {
  width: -webkit-fill-available;
}

.content_1134-1934 {
  padding: 20px 0px 20px 0px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 20px;
  box-sizing: border-box;
  width: -webkit-fill-available;
}
.content_1134-1935 {
  padding: 0px 30px 0px 30px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 20px;
  box-sizing: border-box;
  background: #ffffff;
  width: -webkit-fill-available;
}
.text-chart-review_1134-1936 {
  color: #17181a;
  font-size: 24px;
  font-family: Urbane;
  line-height: 32px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 600;
  text-align: left;
  text-wrap: wrap;
  width: 222px;
}

.demographics_1134-1937 {
  width: 1380px;
}

.table_1134-1938 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 20px;
  box-sizing: border-box;
  width: -webkit-fill-available;
}
.chart-view_1202-12351 {
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
  border-radius: 8px;
  border-color: #f1f5f7;
  border-style: solid;
  border-width: 1px;
  background: #ffffff;
  width: 913px;
  height: 1018px;
}
.pdf_1202-12352 {
  position: relative;
  box-sizing: border-box;
  overflow: hidden;
  background: #e8e8eb;
  width: 872px;
  height: 978px;
}
.image-1_1202-12353 {
  top: 0px;
  left: 1px;
  position: absolute;
  display: flex;
  display: flex;
}

.pdf_1202-12354 {
  top: 0px;
  left: 1px;
  position: absolute;
  display: flex;
  box-sizing: border-box;
  overflow: hidden;
  width: 871px;
  height: 978px;
}
.pdf-pages_1202-12355 {
  top: 26px;
  left: -1px;
  position: absolute;
  display: flex;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  overflow: hidden;
  width: 872px;
  height: 9929px;
}
.page-001_1202-12356 {
  display: flex;
}

.page-002_1202-12357 {
  display: flex;
}

.page-003_1202-12358 {
  display: flex;
}

.page-004_1202-12359 {
  display: flex;
}

.page-005_1202-12360 {
  display: flex;
}

.page-006_1202-12361 {
  display: flex;
}

.page-007_1202-12362 {
  display: flex;
}

.page-008_1202-12363 {
  display: flex;
}

.page-009_1202-12364 {
  display: flex;
}

.page-010_1202-12365 {
  display: flex;
}

.rectangle-22_1202-12366 {
  top: 5px;
  left: 115px;
  position: absolute;
  display: flex;
  display: flex;
}

.rectangle-23_1202-12367 {
  top: 5px;
  left: 138px;
  position: absolute;
  display: flex;
  display: flex;
}

.text-1_1202-12368 {
  top: 2px;
  left: 117px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 9px;
  font-family: Arial;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 400;
  text-align: left;
  text-wrap: nowrap;
  width: 6px;
  height: 20px;
}

.text-10_1202-12369 {
  top: 2px;
  left: 138px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 9px;
  font-family: Arial;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 400;
  text-align: left;
  text-wrap: nowrap;
  width: 11px;
  height: 20px;
}

.frame-912_1134-1959 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-end;
  gap: 20px;
  box-sizing: border-box;
  width: -webkit-fill-available;
}
.hits_1134-1960 {
  width: -webkit-fill-available;
}

.notes_1202-11757 {
  width: -webkit-fill-available;
}

