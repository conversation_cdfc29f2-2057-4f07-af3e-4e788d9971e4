<div class="hits">
  <!-- Header -->
  <div *ngIf="showHeader" class="frame-913">
    <div class="head">
      <div class="stack">
        <span class="text-label">{{ title }}</span>
      </div>
    </div>
  </div>

  <!-- AI Highlights Table -->
  <div class="table">
    <div class="columns">

      <!-- Measure Column -->
      <div class="column">
        <div class="header-item">
          <div class="table-item">
            <span class="text-label">Measure</span>
          </div>
        </div>
        <div *ngFor="let highlight of data; trackBy: trackByHighlightId; let i = index"
             class="table-item"
             [class.highlighted]="highlight.id === selectedHighlightId">
          <div class="icon-text">
            <span class="text-label">{{ highlight.measure }}</span>
          </div>
        </div>
      </div>

      <!-- DoS Column -->
      <div class="column">
        <div class="header-item">
          <div class="table-item">
            <span class="text-label">DoS</span>
          </div>
        </div>
        <div *ngFor="let highlight of data; trackBy: trackByHighlightId; let i = index"
             class="table-item"
             [class.highlighted]="highlight.id === selectedHighlightId">
          <div class="icon-text">
            <span class="text-label">{{ highlight.dateOfService }}</span>
          </div>
        </div>
      </div>

      <!-- Systolic Column -->
      <div class="column">
        <div class="header-item">
          <div class="table-item">
            <span class="text-label">Systolic</span>
          </div>
        </div>
        <div *ngFor="let highlight of data; trackBy: trackByHighlightId; let i = index"
             class="table-item"
             [class.highlighted]="highlight.id === selectedHighlightId">
          <div class="icon-text">
            <span class="text-label">{{ highlight.systolic }}</span>
          </div>
        </div>
      </div>

      <!-- Diastolic Column -->
      <div class="column">
        <div class="header-item">
          <div class="table-item">
            <span class="text-label">Diastolic</span>
          </div>
        </div>
        <div *ngFor="let highlight of data; trackBy: trackByHighlightId; let i = index"
             class="table-item"
             [class.highlighted]="highlight.id === selectedHighlightId">
          <div class="icon-text">
            <span class="text-label">{{ highlight.diastolic }}</span>
          </div>
        </div>
      </div>

      <!-- Page Column -->
      <div class="column">
        <div class="header-item">
          <div class="table-item">
            <span class="text-label">Page</span>
          </div>
        </div>
        <div *ngFor="let highlight of data; trackBy: trackByHighlightId; let i = index"
             class="table-item"
             [class.highlighted]="highlight.id === selectedHighlightId">
          <div class="icon-text">
            <button type="button"
                    class="page-link"
                    (click)="onPageClick(highlight)">
              <span class="text-label">{{ highlight.page }}</span>
            </button>
          </div>
        </div>
      </div>

      <!-- Include Column -->
      <div class="column">
        <div class="column">
          <div class="header-item">
            <div class="table-item">
              <span class="text-label">Include</span>
            </div>
          </div>
          <div *ngFor="let highlight of data; trackBy: trackByHighlightId; let i = index"
               class="table-item"
               [class.highlighted]="highlight.id === selectedHighlightId">
            <app-checkbox
              class="check"
              [ngModel]="highlight.include"
              (ngModelChange)="onIncludeChange(highlight, $event)"
              [attr.aria-label]="'Include highlight ' + (i + 1)">
            </app-checkbox>
          </div>
        </div>
      </div>

    </div>
  </div>
</div>
