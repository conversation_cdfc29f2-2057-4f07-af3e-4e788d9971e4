@use 'variables' as variables;
@use 'mixins' as mix;

.demographics-container {
  width: 100%;
  padding: 20px;
  background: variables.$white;
  border-radius: 8px;
  border: 1px solid variables.$gray-1;
}

.demographics-content {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  gap: 48px;
  width: 100%;
}

.left-section {
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  gap: 48px;
}

.header-section {
  display: flex;
  align-items: flex-start;
  gap: 36px;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  background: none;
  border: none;
  padding: 0;
  transition: opacity 0.2s ease;

  &:hover {
    opacity: 0.8;
  }

  svg {
    width: 21px;
    height: 21px;
  }

  span {
    color: variables.$link;
    font-size: 12px;
    font-family: 'Urbane', sans-serif;
    font-weight: 500;
    line-height: 20px;
  }
}

.member-id-section {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: -4px;
}

.member-id-value {
  color: variables.$text-black;
  font-size: 20px;
  font-family: 'Urbane', sans-serif;
  font-weight: 600;
  line-height: 160%;
}

.member-id-label {
  color: variables.$gray-3;
  font-size: 12px;
  font-family: 'Urbane', sans-serif;
  font-weight: 500;
  line-height: 20px;
}

.right-section {
  display: flex;
  align-items: center;
  gap: 55px;
}

.demographics-group {
  display: flex;
  align-items: center;

  &.basic-demographics {
    gap: 40px;
  }

  &.provider-group {
    gap: 24px;
  }
}

.demographic-item {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: -4px;
  min-width: fit-content;

  // Specific widths based on Figma specifications
  &:nth-child(1) { // Member Name
    width: 57px;
    min-width: 57px;
  }

  &:nth-child(2) { // DOB
    width: 69px;
  }

  &:nth-child(3) { // Gender
    min-width: auto;
  }

  &:nth-child(4) { // LOB
    width: 51px;
  }

  // Provider group specific widths
  .provider-group &:nth-child(1) { // Provider Name
    width: 115px;
    min-width: 115px;
  }

  .provider-group &:nth-child(2) { // NPI
    width: 68px;
  }
}

.demographic-value {
  color: variables.$text-black;
  font-size: 12px;
  font-family: 'Urbane', sans-serif;
  font-weight: 600;
  line-height: 20px;
}

.demographic-label {
  color: variables.$gray-3;
  font-size: 12px;
  font-family: 'Urbane', sans-serif;
  font-weight: 500;
  line-height: 20px;
}

// Responsive design
@include mix.for-phone-only {
  .demographics-content {
    flex-direction: column;
    gap: 24px;
  }

  .right-section {
    flex-direction: column;
    gap: 24px;
    align-items: flex-start;
  }

  .demographics-group {
    flex-wrap: wrap;
    gap: 16px !important;
  }

  .demographic-item {
    min-width: auto;
    width: auto !important;
  }

  .header-section {
    gap: 20px;
  }
}

@include mix.for-tablet-portrait-up {
  .demographics-content {
    flex-direction: row;
  }

  .right-section {
    flex-direction: row;
  }
}
