/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
declare function plural(val: number): number;
declare const _default: (string | number | number[] | (string | undefined)[] | typeof plural | (string[] | undefined)[] | {
    BYN: (string | undefined)[];
    DKK: (string | undefined)[];
    HRK: (string | undefined)[];
    ISK: (string | undefined)[];
    JPY: string[];
    NOK: (string | undefined)[];
    PHP: (string | undefined)[];
    PLN: (string | undefined)[];
    SEK: (string | undefined)[];
    THB: string[];
    TWD: string[];
    ZAR: string[];
} | undefined)[];
export default _default;
